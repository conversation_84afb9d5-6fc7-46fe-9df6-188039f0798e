import axiosInstance from "./axiosInstance";
import config from "../config/environment";
import {
  prepareBloodRequestUpdateData,
  validateBloodRequestData,
  logApiRequest,
  logApiResponse,
} from "../utils/bloodRequestApiUtils";

const BLOOD_REQUEST_API = config.api.bloodRequest;

/**
 * Blood Request Service
 * Handles all API calls related to blood requests
 */
export const bloodRequestService = {
  /**
   * Create a new blood request
   * @param {Object} requestData - Blood request data
   * @returns {Promise} API response
   */
  createBloodRequest: async (requestData) => {
    try {
      console.log("🚀 Calling real API:", BLOOD_REQUEST_API);
      console.log("📤 Request data:", requestData);
      console.log(
        "👤 User ID in request:",
        requestData.userID || requestData.userId
      );

      const response = await axiosInstance.post(BLOOD_REQUEST_API, requestData);

      console.log("✅ API Response:", response.data);
      console.log(
        "🆔 Created request ID:",
        response.data?.requestId || response.data?.id
      );
      console.log(
        "👤 User ID in response:",
        response.data?.userID || response.data?.userId
      );

      return {
        success: true,
        data: response.data,
        message: "Yêu cầu máu đã được tạo thành công",
      };
    } catch (error) {
      console.error("❌ Error creating blood request:", error);
      console.error("📋 Error response data:", error.response?.data);
      console.error("📊 Error status:", error.response?.status);
      console.error("📤 Request data sent:", requestData);

      // Return error for all types of errors (no mock fallback)
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.title ||
          error.message ||
          "Có lỗi xảy ra khi tạo yêu cầu máu",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  /**
   * Get all blood requests (with pagination and filters)
   * @param {Object} params - Query parameters
   * @returns {Promise} API response
   */
  getBloodRequests: async (params = {}) => {
    try {
      const response = await axiosInstance.get(BLOOD_REQUEST_API, { params });
      return {
        success: true,
        data: response.data,
        message: "Lấy danh sách yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error fetching blood requests:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy danh sách yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get blood request by ID
   * @param {string|number} id - Blood request ID
   * @returns {Promise} API response
   */
  getBloodRequestById: async (id) => {
    try {
      const response = await axiosInstance.get(`${BLOOD_REQUEST_API}/${id}`);
      return {
        success: true,
        data: response.data,
        message: "Lấy chi tiết yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error fetching blood request by ID:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy chi tiết yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Update blood request status
   * @param {string|number} id - Blood request ID
   * @param {Object} updateData - Update data
   * @returns {Promise} API response
   */
  updateBloodRequest: async (id, updateData) => {
    try {
      const response = await axiosInstance.get(`${BLOOD_REQUEST_API}/${id}`);
      return {
        success: true,
        data: response.data,
        message: "Lấy thông tin yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error fetching blood request:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy thông tin yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Update blood request
   * @param {string|number} id - Blood request ID
   * @param {Object} updateData - Updated data
   * @returns {Promise} API response
   */
  updateBloodRequest: async (id, updateData) => {
    try {
      const response = await axiosInstance.put(
        `${BLOOD_REQUEST_API}/${id}`,
        updateData
      );
      return {
        success: true,
        data: response.data,
        message: "Cập nhật yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error updating blood request:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi cập nhật yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Delete blood request
   * @param {string|number} id - Blood request ID
   * @returns {Promise} API response
   */
  deleteBloodRequest: async (id) => {
    try {
      const response = await axiosInstance.delete(`${BLOOD_REQUEST_API}/${id}`);
      return {
        success: true,
        data: response.data,
        message: "Xóa yêu cầu máu thành công",
      };
    } catch (error) {
      console.error("Error deleting blood request:", error);
      return {
        success: false,
        error:
          error.response?.data?.message || "Có lỗi xảy ra khi xóa yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Check if user has pending blood request
   * @param {string|number} userId - User ID
   * @returns {Promise} API response with pending status
   */
  checkUserPendingRequest: async (userId) => {
    try {
      console.log("🔍 Checking pending blood requests for user:", userId);

      // Get all blood requests first
      const response = await axiosInstance.get(BLOOD_REQUEST_API);
      console.log("📋 All blood requests for pending check:", response.data);

      // Filter by userID and pending status (status = 0)
      const pendingRequests = response.data.filter(
        (request) =>
          (request.userID === parseInt(userId) ||
            request.userId === parseInt(userId)) &&
          request.status === 0 // Pending status
      );

      console.log("⏳ Pending blood requests for user:", pendingRequests);

      return {
        success: true,
        hasPendingRequest: pendingRequests.length > 0,
        pendingRequest: pendingRequests.length > 0 ? pendingRequests[0] : null,
        message:
          pendingRequests.length > 0
            ? "Bạn đã có đơn đăng ký nhận máu đang chờ xử lý"
            : "Có thể tạo đơn đăng ký mới",
      };
    } catch (error) {
      console.error("❌ Error checking pending blood requests:", error);

      return {
        success: false,
        hasPendingRequest: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi kiểm tra trạng thái đơn đăng ký",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get all blood requests and filter by user ID on frontend
   * @param {string|number} userId - User ID
   * @returns {Promise} API response
   */
  getBloodRequestsByUser: async (userId) => {
    try {
      console.log("🔍 Fetching blood requests for user ID:", userId);

      // Get all blood requests first
      const response = await axiosInstance.get(BLOOD_REQUEST_API);

      console.log("📊 All blood requests from API:", response.data);
      console.log("🔢 Total requests count:", response.data?.length || 0);

      // Filter by userID on frontend
      const userBloodRequests = response.data.filter((request) => {
        const matches =
          request.userID === parseInt(userId) ||
          request.userId === parseInt(userId);

        if (matches) {
          console.log("✅ Found matching request:", request);
        }

        return matches;
      });

      console.log("🎯 Filtered user requests:", userBloodRequests);
      console.log("📈 User requests count:", userBloodRequests.length);

      return {
        success: true,
        data: userBloodRequests,
        message: "Lấy danh sách yêu cầu máu của người dùng thành công",
      };
    } catch (error) {
      console.error("❌ Error fetching blood requests:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi lấy danh sách yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Update blood request status (simple status-only update)
   * @param {string|number} id - Blood request ID
   * @param {string|number} status - New status
   * @returns {Promise} API response
   */
  updateBloodRequestStatus: async (id, status) => {
    try {
      const response = await axiosInstance.patch(
        `${BLOOD_REQUEST_API}/${id}/status`,
        { status }
      );

      return {
        success: true,
        data: response.data,
        message: "Cập nhật trạng thái yêu cầu máu thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi cập nhật trạng thái",
        details: error.response?.data,
      };
    }
  },

  /**
   * Update blood request status with reason (for reject)
   * @param {string|number} id - Blood request ID
   * @param {string|number} status - New status
   * @param {string} reason - Reason for status change
   * @returns {Promise} API response
   */
  updateBloodRequestStatusWithReason: async (id, status, reason) => {
    try {
      const response = await axiosInstance.patch(
        `${BLOOD_REQUEST_API}/${id}/status`,
        { status, reason }
      );

      return {
        success: true,
        data: response.data,
        message: "Cập nhật trạng thái yêu cầu máu thành công",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          "Có lỗi xảy ra khi cập nhật trạng thái",
        details: error.response?.data,
      };
    }
  },

  /**
   * Reject blood request with reason
   * @param {string|number} id - Blood request ID
   * @param {string} reason - Rejection reason
   * @param {Object} requestData - Current request data
   * @returns {Promise} API response
   */
  rejectBloodRequest: async (id, reason, requestData) => {
    try {
      // Strategy 1: Try simple status+reason update first (like blood donation)
      try {
        const statusResponse =
          await bloodRequestService.updateBloodRequestStatusWithReason(
            id,
            3,
            reason
          );

        if (statusResponse.success) {
          return statusResponse;
        }
      } catch (statusError) {
        // Continue to Strategy 2
      }

      // Strategy 2: Fallback to full object PUT (original method)

      // Get current request data first if not provided
      let currentData = requestData;
      if (!currentData) {
        const getResponse = await axiosInstance.get(
          `${BLOOD_REQUEST_API}/${id}`
        );
        currentData = getResponse.data;
      }

      // Prepare and validate data with rejection reason
      const updateData = prepareBloodRequestUpdateData(currentData, 3, reason);

      // Handle patientId foreign key constraint
      if (updateData.patientId && updateData.patientId !== 0) {
        updateData.patientId = null;
      }

      const validation = validateBloodRequestData(updateData);

      if (!validation.isValid) {
        return {
          success: false,
          error: "Dữ liệu không hợp lệ: " + validation.errors.join(", "),
          details: { errors: validation.errors },
        };
      }

      const response = await axiosInstance.put(
        `${BLOOD_REQUEST_API}/${id}`,
        updateData
      );

      return {
        success: true,
        data: response.data,
        message: "Từ chối yêu cầu máu thành công",
      };
    } catch (error) {
      // Handle specific FK constraint error
      if (error.response?.data?.message?.includes("FOREIGN KEY constraint")) {
        return {
          success: false,
          error:
            "Thông tin bệnh nhân không hợp lệ. Vui lòng kiểm tra lại mã bệnh nhân.",
          details: error.response?.data,
        };
      }

      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.title ||
          "Có lỗi xảy ra khi từ chối yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Accept blood request (status = 1)
   * @param {string|number} id - Blood request ID
   * @param {Object} requestData - Current request data
   * @returns {Promise} API response
   */
  acceptBloodRequest: async (id, requestData) => {
    try {
      // Strategy 1: Try simple status update first (like blood donation)
      try {
        const statusResponse =
          await bloodRequestService.updateBloodRequestStatus(id, 1);

        if (statusResponse.success) {
          return statusResponse;
        }
      } catch (statusError) {
        // Continue to Strategy 2
      }

      // Strategy 2: Fallback to full object PUT (original method)

      // Get current request data first if not provided
      let currentData = requestData;
      if (!currentData) {
        const getResponse = await axiosInstance.get(
          `${BLOOD_REQUEST_API}/${id}`
        );
        currentData = getResponse.data;
      }

      // Prepare and validate data
      const updateData = prepareBloodRequestUpdateData(currentData, 1);

      // Handle patientId foreign key constraint
      if (updateData.patientId && updateData.patientId !== 0) {
        updateData.patientId = null;
      }

      const validation = validateBloodRequestData(updateData);

      if (!validation.isValid) {
        return {
          success: false,
          error: "Dữ liệu không hợp lệ: " + validation.errors.join(", "),
          details: { errors: validation.errors },
        };
      }

      const response = await axiosInstance.put(
        `${BLOOD_REQUEST_API}/${id}`,
        updateData
      );

      return {
        success: true,
        data: response.data,
        message: "Chấp nhận yêu cầu máu thành công",
      };
    } catch (error) {
      // Handle specific FK constraint error
      if (error.response?.data?.message?.includes("FOREIGN KEY constraint")) {
        return {
          success: false,
          error:
            "Thông tin bệnh nhân không hợp lệ. Vui lòng kiểm tra lại mã bệnh nhân.",
          details: error.response?.data,
        };
      }

      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.title ||
          "Có lỗi xảy ra khi chấp nhận yêu cầu máu",
        details: error.response?.data,
      };
    }
  },

  /**
   * Complete blood request (status = 2)
   * @param {string|number} id - Blood request ID
   * @param {Object} requestData - Current request data
   * @returns {Promise} API response
   */
  completeBloodRequest: async (id, requestData) => {
    try {
      // Strategy 1: Try simple status update first (like blood donation)
      try {
        const statusResponse =
          await bloodRequestService.updateBloodRequestStatus(id, 2);

        if (statusResponse.success) {
          return statusResponse;
        }
      } catch (statusError) {
        // Continue to Strategy 2
      }

      // Strategy 2: Fallback to full object PUT (original method)

      // Get current request data first if not provided
      let currentData = requestData;
      if (!currentData) {
        const getResponse = await axiosInstance.get(
          `${BLOOD_REQUEST_API}/${id}`
        );
        currentData = getResponse.data;
      }

      // Prepare and validate data
      const updateData = prepareBloodRequestUpdateData(currentData, 2);

      // Handle patientId foreign key constraint
      if (updateData.patientId && updateData.patientId !== 0) {
        updateData.patientId = null;
      }

      const validation = validateBloodRequestData(updateData);

      if (!validation.isValid) {
        return {
          success: false,
          error: "Dữ liệu không hợp lệ: " + validation.errors.join(", "),
          details: { errors: validation.errors },
        };
      }

      const response = await axiosInstance.put(
        `${BLOOD_REQUEST_API}/${id}`,
        updateData
      );

      return {
        success: true,
        data: response.data,
        message: "Hoàn thành yêu cầu máu thành công",
      };
    } catch (error) {
      // Handle specific FK constraint error
      if (error.response?.data?.message?.includes("FOREIGN KEY constraint")) {
        return {
          success: false,
          error:
            "Thông tin bệnh nhân không hợp lệ. Vui lòng kiểm tra lại mã bệnh nhân.",
          details: error.response?.data,
        };
      }

      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.title ||
          "Có lỗi xảy ra khi hoàn thành yêu cầu máu",
        details: error.response?.data,
      };
    }
  },
};

export default bloodRequestService;
