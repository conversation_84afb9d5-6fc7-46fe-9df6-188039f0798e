import { apiClient } from "./axiosInstance";
import vietnamAddressService from "./vietnamAddressService";
import config from "../config/environment";

/**
 * Unified service for managing user information and CRUD operations
 * Combines user API calls with business logic and form processing
 */
class UserInfoService {
  constructor() {
    // API endpoints configuration
    this.endpoints = {
      information: config.api.information || "/Information",
      users: config.api.information || "/Information", // Use same endpoint for consistency
    };
  }

  // ==================== CRUD OPERATIONS ====================

  /**
   * Get all users (for admin purposes)
   * @returns {Promise<Array>} List of all users
   */
  async getAllUsers() {
    try {
      const response = await apiClient.get(this.endpoints.users);
      return response.data;
    } catch (error) {
      console.error("Error fetching all users:", error);
      throw error;
    }
  }

  /**
   * Get all users with force refresh (bypass cache)
   * @returns {Promise<Array>} List of all users
   */
  async getAllUsersForce() {
    try {
      const url = `${this.endpoints.users}?_=${Date.now()}`;
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      console.error("Error force fetching all users:", error);
      throw error;
    }
  }

  /**
   * Fetch user information by user ID
   * @param {string|number} userId - The user ID
   * @returns {Promise<Object>} User information
   */
  async getUserInfo(userId) {
    try {
      const response = await apiClient.get(
        `${this.endpoints.information}/${userId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching user info:", error);
      throw error;
    }
  }

  /**
   * Create a new user
   * @param {Object} userData - User data to create
   * @returns {Promise<Object>} Created user information
   */
  async createUser(userData) {
    try {
      const response = await apiClient.post(this.endpoints.users, userData);
      return response.data;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  /**
   * Update user information
   * @param {string|number} userId - The user ID
   * @param {Object} userData - User data to update
   * @returns {Promise<Object>} Updated user information
   */
  async updateUserInfo(userId, userData) {
    try {
      const response = await apiClient.put(
        `${this.endpoints.information}/${userId}`,
        userData
      );
      return response.data;
    } catch (error) {
      console.error("Error updating user info:", error);
      throw error;
    }
  }

  /**
   * Delete a user by ID
   * @param {string|number} userId - The user ID
   * @returns {Promise<Object>} Deletion response
   */
  async deleteUser(userId) {
    try {
      const response = await apiClient.delete(
        `${this.endpoints.users}/${userId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error deleting user:", error);
      throw error;
    }
  }

  /**
   * Update user (for admin operations like status change)
   * @param {Object} userData - User data to update (must include userID)
   * @returns {Promise<Object>} Update response
   */
  async updateUser(userData) {
    try {
      if (!userData.userID) {
        throw new Error("userID is required for user update");
      }

      const response = await apiClient.put(
        `${this.endpoints.information}/${userData.userID}`,
        userData
      );
      console.log(`User updated successfully:`, userData);
      return response.data;
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }

  /**
   * Check if API returns suspended users
   * @returns {Promise<Object>} Analysis of user statuses
   */
  async analyzeUserStatuses() {
    try {
      const users = await this.getAllUsers();
      const analysis = {
        total: users.length,
        active: users.filter((u) => u.status === 1).length,
        suspended: users.filter((u) => u.status === 0).length,
        other: users.filter((u) => u.status !== 0 && u.status !== 1).length,
        statusDistribution: {},
      };

      // Count status distribution
      users.forEach((user) => {
        const status = user.status;
        analysis.statusDistribution[status] =
          (analysis.statusDistribution[status] || 0) + 1;
      });

      console.log("User Status Analysis:", analysis);
      return analysis;
    } catch (error) {
      console.error("Error analyzing user statuses:", error);
      throw error;
    }
  }

  // Note: API only supports PUT method, not PATCH

  /**
   * Update user distance information
   * @param {string|number} userId - The user ID
   * @param {number} distance - Distance value in kilometers
   * @returns {Promise<Object>} Update response
   */
  async updateUserDistance(userId, distance) {
    try {
      const response = await apiClient.patch(
        `${this.endpoints.information}/${userId}/distance`,
        { distance }
      );
      console.log(
        `Distance updated successfully for user ${userId}:`,
        distance
      );
      return response.data;
    } catch (error) {
      console.error("Error updating user distance:", error);
      throw error;
    }
  }

  // ==================== ADDRESS MANAGEMENT ====================

  /**
   * Fetch provinces from local JSON file
   * @returns {Promise<Array>} List of provinces
   */
  async getProvinces() {
    return vietnamAddressService.getProvinces();
  }

  /**
   * Fetch wards for a specific province from local JSON file
   * @param {string} provinceName - The province name
   * @returns {Promise<Array>} List of wards
   */
  async getWardsByProvince(provinceName) {
    return vietnamAddressService.getWardsByProvince(provinceName);
  }

  /**
   * Preload all address data for better performance
   * @returns {Promise<void>}
   */
  async preloadAddressData() {
    return vietnamAddressService.preloadAllData();
  }

  /**
   * Clear address cache
   * @returns {void}
   */
  clearAddressCache() {
    vietnamAddressService.clearCache();
  }

  // ==================== UTILITY FUNCTIONS ====================

  /**
   * Format date from API response (removes time part)
   * @param {string} dateString - Date string from API
   * @returns {string} Formatted date (YYYY-MM-DD)
   */
  formatDate(dateString) {
    return dateString ? dateString.split("T")[0] : "";
  }

  /**
   * Extract house number and street from full address
   * @param {string} fullAddress - Full address string
   * @param {string} wardName - Ward name to remove
   * @param {string} provinceName - Province name to remove
   * @returns {string} House number and street only
   */
  extractHouseNumberAndStreet(fullAddress, wardName, provinceName) {
    if (!fullAddress) return "";

    // Split full address by comma
    const parts = fullAddress.split(",").map((part) => part.trim());

    // Remove ward and province from address
    const filteredParts = parts.filter((part) => {
      return part !== wardName && part !== provinceName;
    });

    // Join house number and street
    return filteredParts.join(", ");
  }

  /**
   * Calculate age from date of birth
   * @param {string} dateOfBirth - Date of birth (YYYY-MM-DD)
   * @returns {number} Age in years
   */
  calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  }

  /**
   * Save calculated distance to database for current user
   * @param {number} distance - Distance value in kilometers
   * @returns {Promise<Object>} Save result
   */
  async saveDistanceForCurrentUser(distance) {
    try {
      // Get current user from localStorage
      const currentUser = JSON.parse(
        localStorage.getItem("currentUser") || "{}"
      );

      if (!currentUser.id) {
        throw new Error("Không tìm thấy thông tin người dùng hiện tại");
      }

      // Update distance in database
      const result = await this.updateUserDistance(currentUser.id, distance);

      // Update current user in localStorage with new distance
      const updatedUser = {
        ...currentUser,
        distance: distance,
      };
      localStorage.setItem("currentUser", JSON.stringify(updatedUser));

      return {
        success: true,
        data: result,
        message: "Đã lưu khoảng cách thành công",
      };
    } catch (error) {
      console.error("Error saving distance for current user:", error);
      return {
        success: false,
        error: error.message || "Có lỗi xảy ra khi lưu khoảng cách",
        details: error,
      };
    }
  }

  /**
   * Build full address from components
   * @param {string} houseNumber - House number and street
   * @param {string} wardName - Ward name
   * @param {string} provinceName - Province name
   * @returns {string} Full address
   */
  buildFullAddress(houseNumber, wardName, provinceName) {
    return [houseNumber, wardName, provinceName].filter(Boolean).join(", ");
  }

  /**
   * Prepare user data for API submission
   * @param {Object} formData - Form data
   * @param {Object} currentUser - Current user object
   * @returns {Object} Formatted data for API
   */
  prepareUserDataForSubmission(formData, currentUser) {
    // Create full address from components
    const fullAddress = this.buildFullAddress(
      formData.address,
      formData.wardName,
      formData.provinceName
    );

    return {
      userID: parseInt(currentUser.id),
      email: formData.email || "",
      password: currentUser.password || "",
      phone: formData.phone || "",
      idCardType: formData.documentType || "",
      idCard: formData.documentNumber || "",
      name: formData.fullName || "",
      dateOfBirth: formData.dob || null,
      age: formData.dob ? this.calculateAge(formData.dob) : null,
      gender: formData.gender || "",
      city: formData.provinceName || "",
      district: formData.provinceName || "", // Use province as district (database requirement)
      ward: formData.wardName || "",
      address: fullAddress || "",
      distance: null,
      bloodGroup: formData.bloodType || "",
      rhType: formData.rhType || "",
      weight: null,
      height: null,
      status: 1,
      roleID: currentUser.roleID || 1,
      department: currentUser.department || "",
      createdAt: new Date().toISOString(),
    };
  }

  /**
   * Validate user data before submission
   * @param {Object} userData - User data to validate
   * @returns {Object} Validation result with isValid and errors
   */
  validateUserData(userData) {
    const errors = {};

    // Required fields validation
    if (!userData.name?.trim()) {
      errors.name = "Họ và tên là bắt buộc";
    }

    if (!userData.email?.trim() && !userData.phone?.trim()) {
      errors.contact = "Cần có ít nhất email hoặc số điện thoại";
    }

    // Email validation
    if (userData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
      errors.email = "Email không hợp lệ";
    }

    // Phone validation
    if (userData.phone && !/^0\d{9}$/.test(userData.phone)) {
      errors.phone = "Số điện thoại không hợp lệ (10 số, bắt đầu bằng 0)";
    }

    // ID card validation
    if (userData.idCard) {
      if (userData.idCardType === "cccd" && !/^\d{12}$/.test(userData.idCard)) {
        errors.idCard = "CCCD phải gồm đúng 12 số";
      } else if (
        userData.idCardType === "passport" &&
        !/^[A-Z]\d{7}$/.test(userData.idCard)
      ) {
        errors.idCard = "Hộ chiếu phải gồm 1 chữ cái in hoa và 7 số";
      }
    }

    // Date of birth validation
    if (userData.dateOfBirth) {
      const birthDate = new Date(userData.dateOfBirth);
      const today = new Date();
      if (birthDate > today) {
        errors.dateOfBirth = "Ngày sinh không thể trong tương lai";
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  /**
   * Search users by criteria
   * @param {Object} criteria - Search criteria
   * @returns {Promise<Array>} Filtered users
   */
  async searchUsers(criteria = {}) {
    try {
      const allUsers = await this.getAllUsers();

      if (!criteria || Object.keys(criteria).length === 0) {
        return allUsers;
      }

      return allUsers.filter((user) => {
        return Object.entries(criteria).every(([key, value]) => {
          if (!value) return true; // Skip empty criteria

          const userValue = user[key];
          if (typeof userValue === "string") {
            return userValue.toLowerCase().includes(value.toLowerCase());
          }
          return userValue === value;
        });
      });
    } catch (error) {
      console.error("Error searching users:", error);
      throw error;
    }
  }

  /**
   * Get user statistics
   * @returns {Promise<Object>} User statistics
   */
  async getUserStatistics() {
    try {
      const users = await this.getAllUsers();

      const stats = {
        total: users.length,
        active: users.filter((u) => u.status === 1).length,
        inactive: users.filter((u) => u.status === 0).length,
        byGender: {
          male: users.filter((u) => u.gender === "male").length,
          female: users.filter((u) => u.gender === "female").length,
          other: users.filter((u) => u.gender === "other").length,
        },
        byBloodType: {},
        byProvince: {},
      };

      // Count by blood type
      users.forEach((user) => {
        if (user.bloodGroup) {
          stats.byBloodType[user.bloodGroup] =
            (stats.byBloodType[user.bloodGroup] || 0) + 1;
        }
      });

      // Count by province
      users.forEach((user) => {
        if (user.city) {
          stats.byProvince[user.city] = (stats.byProvince[user.city] || 0) + 1;
        }
      });

      return stats;
    } catch (error) {
      console.error("Error getting user statistics:", error);
      throw error;
    }
  }

  /**
   * Check if user profile is complete for blood donation/request
   * @param {Object} userInfo - User information object
   * @returns {Object} Profile completeness result
   */
  checkProfileCompleteness(userInfo) {
    if (!userInfo) {
      return {
        isComplete: false,
        missingFields: ["Không tìm thấy thông tin người dùng"],
        message: "Không thể kiểm tra thông tin hồ sơ",
      };
    }

    const missingFields = [];
    const requiredFields = [
      { field: "phone", label: "Số điện thoại" },
      { field: "dateOfBirth", label: "Ngày sinh" },
      { field: "gender", label: "Giới tính" },
      { field: "address", label: "Địa chỉ" },
      { field: "city", label: "Tỉnh/Thành phố" },
      // Removed bloodGroup and rhType from required fields
      // { field: "bloodGroup", label: "Nhóm máu" },
    ];

    // Check required fields
    requiredFields.forEach(({ field, label }) => {
      if (!userInfo[field] || userInfo[field].toString().trim() === "") {
        missingFields.push(label);
      }
    });

    // Check specific validations
    if (userInfo.phone && !/^0\d{9}$/.test(userInfo.phone)) {
      missingFields.push("Số điện thoại không hợp lệ");
    }

    if (userInfo.dateOfBirth) {
      const age = this.calculateAge(userInfo.dateOfBirth);
      if (age < 18 || age > 65) {
        missingFields.push("Tuổi phải từ 18-65 để hiến máu");
      }
    }

    const isComplete = missingFields.length === 0;

    return {
      isComplete,
      missingFields,
      message: isComplete
        ? "Hồ sơ đã đầy đủ thông tin"
        : `Còn thiếu ${missingFields.length} thông tin bắt buộc`,
    };
  }
}

// Export singleton instance
export default new UserInfoService();
