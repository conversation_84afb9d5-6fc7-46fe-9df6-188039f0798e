import { useState, useEffect, useCallback } from "react";
import { message } from "antd";
import authService from "../services/authService";
import bloodDonationService from "../services/bloodDonationService";
import { bloodRequestService } from "../services/bloodRequestService";
import { DONATION_STATUS, REQUEST_STATUS } from "../constants/systemConstants";

/**
 * Custom hook để quản lý dữ liệu activities
 * Giữ nguyên logic từ file gốc
 */
const useActivityData = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState("all");

  const currentUser = authService.getCurrentUser();

  // Get user blood type from profile - giữ nguyên logic gốc
  const getUserBloodType = useCallback(() => {
    try {
      const memberInfo = JSON.parse(localStorage.getItem("memberInfo") || "{}");
      const bloodGroup =
        memberInfo.bloodGroup || currentUser?.profile?.bloodGroup;
      const rhType = memberInfo.rhType || currentUser?.profile?.rhType;

      if (bloodGroup && rhType) {
        const rhSymbol =
          rhType === "Rh+" || rhType === "+"
            ? "+"
            : rhType === "Rh-" || rhType === "-"
            ? "-"
            : rhType;
        return `${bloodGroup}${rhSymbol}`;
      } else if (bloodGroup) {
        return bloodGroup;
      } else {
        return "Chưa xác định";
      }
    } catch (error) {
      console.error("Error getting user blood type:", error);
      return "Chưa xác định";
    }
  }, [currentUser]);

  // Helper function to map API status - giữ nguyên logic gốc
  const mapApiStatusToDisplayStatus = useCallback((apiStatus) => {
    const statusMap = {
      0: DONATION_STATUS.REGISTERED,
      1: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      2: DONATION_STATUS.HEALTH_CHECKED,
      3: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      registered: DONATION_STATUS.REGISTERED,
      pending: DONATION_STATUS.REGISTERED,
      health_checked: DONATION_STATUS.HEALTH_CHECKED,
      not_eligible_health: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      donated: DONATION_STATUS.DONATED,
      blood_tested: DONATION_STATUS.BLOOD_TESTED,
      not_eligible_test: DONATION_STATUS.NOT_ELIGIBLE_TEST,
      completed: DONATION_STATUS.COMPLETED,
      stored: DONATION_STATUS.STORED,
      approved: DONATION_STATUS.HEALTH_CHECKED,
      rejected: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      cancelled: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      fulfilled: REQUEST_STATUS.FULFILLED,
    };

    const lookupKey = typeof apiStatus === "number" ? apiStatus : apiStatus;
    return statusMap[lookupKey] || DONATION_STATUS.REGISTERED;
  }, []);

  // Helper function to map blood request status - giữ nguyên logic gốc
  const mapBloodRequestStatusToDisplayStatus = useCallback((apiStatus) => {
    const statusMap = {
      // String status mapping
      pending: REQUEST_STATUS.PENDING,
      approved: REQUEST_STATUS.APPROVED,
      completed: REQUEST_STATUS.FULFILLED,
      rejected: REQUEST_STATUS.REJECTED,
      cancelled: REQUEST_STATUS.CANCELLED,
      canceled: REQUEST_STATUS.CANCELLED,

      // Numeric status mapping (correct according to bloodRequestUtils.js)
      0: REQUEST_STATUS.PENDING, // Chờ duyệt
      1: REQUEST_STATUS.APPROVED, // Hợp lệ
      2: REQUEST_STATUS.FULFILLED, // Hoàn thành
      3: REQUEST_STATUS.REJECTED, // Từ chối
      4: REQUEST_STATUS.CANCELLED, // Đã xóa
    };

    const lookupKey = typeof apiStatus === "number" ? apiStatus : apiStatus;
    return statusMap[lookupKey] || REQUEST_STATUS.PENDING;
  }, []);

  // Load activity history - giữ nguyên logic gốc
  const loadActivityHistory = useCallback(async () => {
    setLoading(true);
    try {
      const userBloodType = getUserBloodType();

      // Skip blood donation appointments for now (API not available)
      const appointmentsData = [];

      // Lấy thông tin yêu cầu máu từ API
      const bloodRequestsResponse =
        await bloodRequestService.getBloodRequestsByUser(currentUser.id);

      const bloodRequestsData = bloodRequestsResponse.success
        ? bloodRequestsResponse.data
        : [];

      // Kiểm tra nếu không có dữ liệu blood requests
      if (!bloodRequestsData || bloodRequestsData.length === 0) {
        setActivities([]);
        return;
      }

      // Chuyển đổi dữ liệu từ API thành format hiển thị - giữ nguyên logic gốc
      const donationActivities = (appointmentsData || []).map(
        (appointment, index) => {
          const donationDate =
            appointment.AppointmentDate ||
            appointment.requestedDonationDate ||
            appointment.appointmentDate;

          return {
            id: appointment.appointmentId || appointment.id || `temp-${index}`,
            type: "donation",
            title: "Đặt lịch hiến máu",
            status: mapApiStatusToDisplayStatus(appointment.status),
            bloodType: `${userBloodType}${
              currentUser?.rhType
                ? currentUser.rhType.includes("+") ||
                  currentUser.rhType.includes("-")
                  ? currentUser.rhType.replace("Rh", "")
                  : currentUser.rhType
                : ""
            }`,
            quantity: appointment.quantity || "450ml",
            appointmentDate: donationDate,
            timeSlot:
              appointment.TimeSlot || appointment.timeSlot || "Chưa xác định",
            location:
              appointment.location ||
              "Bệnh viện Đa khoa Ánh Dương - Khoa Huyết học, Tầng 2",
            notes: appointment.Notes || appointment.notes || "",
            doctorNotes:
              appointment.DoctorNotes || appointment.doctorNotes || "",
            weight: appointment.Weight || appointment.weight || 0,
            height: appointment.Height || appointment.height || 0,
            hasDonated: appointment.hasDonated,
            lastDonationDate:
              appointment.LastDonationDate || appointment.lastDonationDate,
            createdAt:
              appointment.CreatedAt ||
              appointment.createdAt ||
              appointment.requestedDonationDate,
            completedAt: appointment.completedAt || null,
            isCancelled:
              appointment.Cancel === 1 ||
              appointment.Cancel === true ||
              appointment.cancel === 1 ||
              appointment.cancel === true ||
              appointment.cancelled === true ||
              appointment.cancelled === 1,
            cancelledAt:
              appointment.CancelledAt || appointment.cancelledAt || null,
          };
        }
      );

      // Chuyển đổi dữ liệu blood requests - fetch individual details for accurate status
      const requestActivities = [];

      for (const request of bloodRequestsData || []) {
        try {
          // Fetch individual request details to get current status and timestamps
          const requestId = request.requestId || request.id;
          if (requestId) {
            const detailResponse =
              await bloodRequestService.getBloodRequestById(requestId);
            const detailData = detailResponse.success
              ? detailResponse.data
              : request;

            // Use detailed data if available, fallback to original data
            const finalData = detailResponse.success ? detailData : request;

            requestActivities.push({
              id: requestId,
              type: "request",
              title: "Yêu cầu nhận máu",
              status: mapBloodRequestStatusToDisplayStatus(finalData.status),
              bloodType: `${
                finalData.bloodGroup ||
                finalData.bloodType ||
                request.bloodGroup
              }${
                finalData.rhType || request.rhType
                  ? (finalData.rhType || request.rhType).includes("+") ||
                    (finalData.rhType || request.rhType).includes("-")
                    ? (finalData.rhType || request.rhType).replace("Rh", "")
                    : finalData.rhType || request.rhType
                  : ""
              }`,
              quantity: `${finalData.quantity || request.quantity || 0}${
                finalData.unit || request.unit || "ml"
              }`,
              patientName: finalData.patientName || request.patientName,
              patientAge: finalData.age || request.age,
              patientGender: finalData.gender || request.gender,
              relationship: finalData.relationship || request.relationship,
              hospitalName:
                finalData.facilityName ||
                finalData.hospitalName ||
                request.facilityName ||
                request.hospitalName,
              doctorName: finalData.doctorName || request.doctorName,
              doctorPhone: finalData.doctorPhone || request.doctorPhone,
              medicalCondition:
                finalData.reason ||
                finalData.medicalCondition ||
                request.reason ||
                request.medicalCondition,
              urgency: finalData.urgency || request.urgency || "normal",
              notes: finalData.notes || request.notes || "",
              createdAt:
                finalData.createdTime ||
                finalData.createdAt ||
                request.createdTime ||
                request.createdAt,
              completedAt: finalData.completedAt || request.completedAt || null,
              isCancelled:
                finalData.status === "cancelled" ||
                finalData.status === "canceled" ||
                request.status === "cancelled" ||
                request.status === "canceled",
              cancelledAt: finalData.cancelledAt || request.cancelledAt || null,
            });
          } else {
            // Fallback for requests without ID
            requestActivities.push({
              id: `request-${requestActivities.length}`,
              type: "request",
              title: "Yêu cầu nhận máu",
              status: mapBloodRequestStatusToDisplayStatus(request.status),
              bloodType: `${request.bloodGroup || request.bloodType}${
                request.rhType
                  ? request.rhType.includes("+") || request.rhType.includes("-")
                    ? request.rhType.replace("Rh", "")
                    : request.rhType
                  : ""
              }`,
              quantity: `${request.quantity || 0}${request.unit || "ml"}`,
              patientName: request.patientName,
              patientAge: request.age,
              patientGender: request.gender,
              relationship: request.relationship,
              hospitalName: request.facilityName || request.hospitalName,
              doctorName: request.doctorName,
              doctorPhone: request.doctorPhone,
              medicalCondition: request.reason || request.medicalCondition,
              urgency: request.urgency || "normal",
              notes: request.notes || "",
              createdAt: request.createdTime || request.createdAt,
              completedAt: request.completedAt || null,
              isCancelled:
                request.status === "cancelled" || request.status === "canceled",
              cancelledAt: request.cancelledAt || null,
            });
          }
        } catch (error) {
          console.error(
            `Error fetching details for request ${
              request.requestId || request.id
            }:`,
            error
          );
          // Fallback to original data if detail fetch fails
          requestActivities.push({
            id:
              request.requestId ||
              request.id ||
              `request-${requestActivities.length}`,
            type: "request",
            title: "Yêu cầu nhận máu",
            status: mapBloodRequestStatusToDisplayStatus(request.status),
            bloodType: `${request.bloodGroup || request.bloodType}${
              request.rhType
                ? request.rhType.includes("+") || request.rhType.includes("-")
                  ? request.rhType.replace("Rh", "")
                  : request.rhType
                : ""
            }`,
            quantity: `${request.quantity || 0}${request.unit || "ml"}`,
            patientName: request.patientName,
            patientAge: request.age,
            patientGender: request.gender,
            relationship: request.relationship,
            hospitalName: request.facilityName || request.hospitalName,
            doctorName: request.doctorName,
            doctorPhone: request.doctorPhone,
            medicalCondition: request.reason || request.medicalCondition,
            urgency: request.urgency || "normal",
            notes: request.notes || "",
            createdAt: request.createdTime || request.createdAt,
            completedAt: request.completedAt || null,
            isCancelled:
              request.status === "cancelled" || request.status === "canceled",
            cancelledAt: request.cancelledAt || null,
          });
        }
      }

      // Kết hợp và sắp xếp - chỉ blood requests (donation appointments disabled)
      const allActivities = [...requestActivities];
      allActivities.sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      );

      setActivities(allActivities);
    } catch (error) {
      console.error("Error loading activity history:", error);
      setActivities([]);
    } finally {
      setLoading(false);
    }
  }, [
    currentUser,
    getUserBloodType,
    mapApiStatusToDisplayStatus,
    mapBloodRequestStatusToDisplayStatus,
  ]);

  // Cancel appointment - giữ nguyên logic gốc
  const handleCancelAppointment = useCallback(
    async (appointmentId) => {
      if (!window.confirm("Bạn có chắc chắn muốn hủy lịch hẹn này không?")) {
        return;
      }

      try {
        setLoading(true);
        console.log("🚀 Cancelling appointment:", appointmentId);
        await bloodDonationService.cancelAppointment(appointmentId);
        console.log("✅ Appointment cancelled successfully");

        message.success("Đã hủy lịch hẹn thành công!");

        console.log("🔄 Reloading activity history...");
        await loadActivityHistory();
        console.log("✅ Activity history reloaded");
      } catch (error) {
        console.error("❌ Error cancelling appointment:", error);
        message.error("Có lỗi xảy ra khi hủy lịch hẹn. Vui lòng thử lại.");
      } finally {
        setLoading(false);
      }
    },
    [loadActivityHistory]
  );

  // Filter activities - giữ nguyên logic gốc
  const getFilteredActivities = useCallback(() => {
    switch (filter) {
      case "donations":
        return activities.filter((a) => a.type === "donation");
      case "requests":
        return activities.filter((a) => a.type === "request");
      default:
        return activities;
    }
  }, [activities, filter]);

  // Statistics - giữ nguyên logic gốc
  const donationCount = activities.filter((a) => a.type === "donation").length;
  const requestCount = activities.filter((a) => a.type === "request").length;
  const completedCount = activities.filter((a) =>
    [
      DONATION_STATUS.COMPLETED,
      REQUEST_STATUS.COMPLETED,
      REQUEST_STATUS.FULFILLED,
    ].includes(a.status)
  ).length;

  useEffect(() => {
    loadActivityHistory();
  }, [loadActivityHistory]);

  return {
    // Data
    activities,
    filteredActivities: getFilteredActivities(),
    loading,
    filter,

    // Statistics
    donationCount,
    requestCount,
    completedCount,

    // Actions
    setFilter,
    loadActivityHistory,
    handleCancelAppointment,

    // Helpers
    getUserBloodType,
  };
};

export default useActivityData;
