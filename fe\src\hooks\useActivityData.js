import { useState, useEffect, useCallback } from "react";
import { message } from "antd";
import authService from "../services/authService";
import bloodDonationService from "../services/bloodDonationService";
import { bloodRequestService } from "../services/bloodRequestService";
import { DONATION_STATUS, REQUEST_STATUS } from "../constants/systemConstants";

/**
 * Custom hook để quản lý dữ liệu activities
 * Giữ nguyên logic từ file gốc
 */
const useActivityData = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState("all");

  const currentUser = authService.getCurrentUser();

  // Get user blood type from profile - giữ nguyên logic gốc
  const getUserBloodType = useCallback(() => {
    try {
      const memberInfo = JSON.parse(localStorage.getItem("memberInfo") || "{}");
      const bloodGroup =
        memberInfo.bloodGroup || currentUser?.profile?.bloodGroup;
      const rhType = memberInfo.rhType || currentUser?.profile?.rhType;

      if (bloodGroup && rhType) {
        const rhSymbol =
          rhType === "Rh+" || rhType === "+"
            ? "+"
            : rhType === "Rh-" || rhType === "-"
              ? "-"
              : rhType;
        return `${bloodGroup}${rhSymbol}`;
      } else if (bloodGroup) {
        return bloodGroup;
      } else {
        return "Chưa xác định";
      }
    } catch (error) {
      console.error("Error getting user blood type:", error);
      return "Chưa xác định";
    }
  }, [currentUser]);

  // Helper function to map API status - giữ nguyên logic gốc
  const mapApiStatusToDisplayStatus = useCallback((apiStatus) => {
    const statusMap = {
      0: DONATION_STATUS.REGISTERED,
      1: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      2: DONATION_STATUS.HEALTH_CHECKED,
      3: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      registered: DONATION_STATUS.REGISTERED,
      pending: DONATION_STATUS.REGISTERED,
      health_checked: DONATION_STATUS.HEALTH_CHECKED,
      not_eligible_health: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      donated: DONATION_STATUS.DONATED,
      blood_tested: DONATION_STATUS.BLOOD_TESTED,
      not_eligible_test: DONATION_STATUS.NOT_ELIGIBLE_TEST,
      completed: DONATION_STATUS.COMPLETED,
      stored: DONATION_STATUS.STORED,
      approved: DONATION_STATUS.HEALTH_CHECKED,
      rejected: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      cancelled: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
      fulfilled: REQUEST_STATUS.FULFILLED,
    };

    const lookupKey = typeof apiStatus === "number" ? apiStatus : apiStatus;
    return statusMap[lookupKey] || DONATION_STATUS.REGISTERED;
  }, []);

  // Helper function to map blood request status - giữ nguyên logic gốc
  const mapBloodRequestStatusToDisplayStatus = useCallback((apiStatus) => {
    const statusMap = {
      // String status mapping
      pending: REQUEST_STATUS.PENDING,
      approved: REQUEST_STATUS.APPROVED,
      completed: REQUEST_STATUS.FULFILLED,
      rejected: REQUEST_STATUS.REJECTED,
      cancelled: REQUEST_STATUS.CANCELLED,
      canceled: REQUEST_STATUS.CANCELLED,

      // Numeric status mapping (correct according to bloodRequestUtils.js)
      0: REQUEST_STATUS.PENDING, // Chờ duyệt
      1: REQUEST_STATUS.APPROVED, // Hợp lệ
      2: REQUEST_STATUS.FULFILLED, // Hoàn thành
      3: REQUEST_STATUS.REJECTED, // Từ chối
      4: REQUEST_STATUS.CANCELLED, // Đã xóa
    };

    const lookupKey = typeof apiStatus === "number" ? apiStatus : apiStatus;
    return statusMap[lookupKey] || REQUEST_STATUS.PENDING;
  }, []);

  // Load activity history - cập nhật để lấy lịch sử hiến máu
  const loadActivityHistory = useCallback(async () => {
    setLoading(true);
    try {
      const userBloodType = getUserBloodType();

      // Lấy lịch sử hiến máu từ API
      let appointmentsData = [];
      try {
        console.log("🩸 Fetching blood donation appointments for user:", currentUser.id);
        appointmentsData = await bloodDonationService.getAppointmentsByUser(currentUser.id);
        console.log("✅ Blood donation appointments loaded:", appointmentsData?.length || 0);
      } catch (appointmentError) {
        console.error("❌ Error loading blood donation appointments:", appointmentError);
        appointmentsData = [];
      }

      // Lấy thông tin yêu cầu máu từ API
      let bloodRequestsData = [];
      try {
        const bloodRequestsResponse =
          await bloodRequestService.getBloodRequestsByUser(currentUser.id);
        bloodRequestsData = bloodRequestsResponse.success
          ? bloodRequestsResponse.data
          : [];
        console.log("✅ Blood requests loaded:", bloodRequestsData?.length || 0);
      } catch (requestError) {
        console.error("❌ Error loading blood requests:", requestError);
        bloodRequestsData = [];
      }

      // Kiểm tra nếu không có dữ liệu blood requests
      if (!bloodRequestsData || bloodRequestsData.length === 0) {
        setActivities([]);
        return;
      }

      // Chuyển đổi dữ liệu từ API thành format hiển thị - giữ nguyên logic gốc
      const donationActivities = (appointmentsData || []).map(
        (appointment, index) => {
          const donationDate =
            appointment.AppointmentDate ||
            appointment.requestedDonationDate ||
            appointment.appointmentDate;

          return {
            id: appointment.appointmentId || appointment.id || `temp-${index}`,
            type: "donation",
            title: "Đặt lịch hiến máu",
            status: mapApiStatusToDisplayStatus(appointment.status),
            bloodType: `${userBloodType}${currentUser?.rhType
              ? currentUser.rhType.includes("+") ||
                currentUser.rhType.includes("-")
                ? currentUser.rhType.replace("Rh", "")
                : currentUser.rhType
              : ""
              }`,
            quantity: appointment.quantity || "450ml",
            appointmentDate: donationDate,
            timeSlot:
              appointment.TimeSlot || appointment.timeSlot || "Chưa xác định",
            location:
              appointment.location ||
              "Bệnh viện Đa khoa Ánh Dương - Khoa Huyết học, Tầng 2",
            notes: appointment.Notes || appointment.notes || "",
            doctorNotes:
              appointment.DoctorNotes || appointment.doctorNotes || "",
            weight: appointment.Weight || appointment.weight || 0,
            height: appointment.Height || appointment.height || 0,
            hasDonated: appointment.hasDonated,
            lastDonationDate:
              appointment.LastDonationDate || appointment.lastDonationDate,
            // Thông tin sức khỏe chi tiết từ bác sĩ
            healthCheck: {
              heartRate: appointment.HeartRate || appointment.heartRate || "",
              bloodPressure: appointment.BloodPressure || appointment.bloodPressure || "",
              hemoglobin: appointment.Hemoglobin || appointment.hemoglobin || "",
              temperature: appointment.Temperature || appointment.temperature || "",
              weight: appointment.Weight || appointment.weight || 0,
              height: appointment.Height || appointment.height || 0,
            },
            createdAt:
              appointment.CreatedAt ||
              appointment.createdAt ||
              appointment.requestedDonationDate,
            completedAt: appointment.completedAt || null,
            isCancelled:
              appointment.Cancel === 1 ||
              appointment.Cancel === true ||
              appointment.cancel === 1 ||
              appointment.cancel === true ||
              appointment.cancelled === true ||
              appointment.cancelled === 1,
            cancelledAt:
              appointment.CancelledAt || appointment.cancelledAt || null,
          };
        }
      );

      // Chuyển đổi dữ liệu blood requests - sử dụng trực tiếp dữ liệu từ API
      const requestActivities = (bloodRequestsData || []).map(
        (request, index) => {
          const requestId =
            request.requestId || request.id || `request-${index}`;

          // Tạo statusHistory từ thông tin thực tế từ API
          const statusHistory = [];
          const createdTime = request.createdTime || request.createdAt;

          // Luôn có bước tạo yêu cầu
          statusHistory.push({
            status: 0, // Pending
            updatedAt: createdTime,
            notes: "Yêu cầu được tạo thành công",
          });

          // Thêm các bước tiếp theo - ẩn thời gian để đảm bảo tính chính xác
          if (request.status >= 1) {
            // Đã chấp nhận - ẩn thời gian vì backend chưa hỗ trợ timestamp riêng biệt
            statusHistory.push({
              status: 1,
              updatedAt: null, // Ẩn thời gian để tránh hiển thị sai
              hasRealTimestamp: false, // Không hiển thị thời gian
              notes:
                request.acceptedNotes ||
                "Yêu cầu đã được chấp nhận bởi bác sĩ khoa huyết học",
              updatedBy: request.acceptedBy || "Bác sĩ khoa huyết học",
            });
          }

          if (request.status >= 2) {
            // Hoàn thành - ẩn thời gian vì backend chưa hỗ trợ timestamp riêng biệt
            statusHistory.push({
              status: 2,
              updatedAt: null, // Ẩn thời gian để tránh hiển thị sai
              hasRealTimestamp: false, // Không hiển thị thời gian
              notes:
                request.completedNotes || "Máu đã được xuất kho và hoàn thành",
              updatedBy: request.completedBy || "Manager",
            });
          }

          if (request.status === 3) {
            // Từ chối - ẩn thời gian vì backend chưa hỗ trợ timestamp riêng biệt
            statusHistory.push({
              status: 3,
              updatedAt: null, // Ẩn thời gian để tránh hiển thị sai
              hasRealTimestamp: false, // Không hiển thị thời gian
              notes:
                request.rejectionReason ||
                request.rejectedNotes ||
                "Yêu cầu bị từ chối",
              updatedBy: request.rejectedBy || "Bác sĩ khoa huyết học",
            });
          }

          return {
            id: requestId,
            type: "request",
            title: "Yêu cầu nhận máu",
            status: mapBloodRequestStatusToDisplayStatus(request.status),
            bloodType: `${request.bloodGroup || request.bloodType}${
              request.rhType
                ? request.rhType.includes("+") || request.rhType.includes("-")
                  ? request.rhType.replace("Rh", "")
                  : request.rhType
                : ""
            }`,
            quantity: `${request.quantity || 0}${request.unit || "ml"}`,
            patientName: request.patientName,
            patientAge: request.age,
            patientGender: request.gender,
            relationship: request.relationship,
            hospitalName: request.facilityName || request.hospitalName,
            doctorName: request.doctorName,
            doctorPhone: request.doctorPhone,
            medicalCondition: request.reason || request.medicalCondition,
            urgency: request.urgency || "normal",
            notes: request.notes || "",
            createdAt: request.createdTime || request.createdAt,
            updatedAt:
              request.updatedTime || request.createdTime || request.createdAt,
            completedAt: request.completedAt || null,
            isCancelled:
              request.status === "cancelled" || request.status === "canceled",
            cancelledAt: request.cancelledAt || null,
          };
        }
      );

      // Kết hợp và sắp xếp - bao gồm cả donation appointments và blood requests
      const allActivities = [...donationActivities, ...requestActivities];
      allActivities.sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      );

      console.log("📊 Total activities loaded:", {
        donations: donationActivities.length,
        requests: requestActivities.length,
        total: allActivities.length
      });

      setActivities(allActivities);
    } catch (error) {
      console.error("Error loading activity history:", error);
      setActivities([]);
    } finally {
      setLoading(false);
    }
  }, [
    currentUser,
    getUserBloodType,
    mapApiStatusToDisplayStatus,
    mapBloodRequestStatusToDisplayStatus,
  ]);

  // Cancel appointment - giữ nguyên logic gốc
  const handleCancelAppointment = useCallback(
    async (appointmentId) => {
      if (!window.confirm("Bạn có chắc chắn muốn hủy lịch hẹn này không?")) {
        return;
      }

      try {
        setLoading(true);
        console.log("🚀 Cancelling appointment:", appointmentId);
        await bloodDonationService.cancelAppointment(appointmentId);
        console.log("✅ Appointment cancelled successfully");

        message.success("Đã hủy lịch hẹn thành công!");

        console.log("🔄 Reloading activity history...");
        await loadActivityHistory();
        console.log("✅ Activity history reloaded");
      } catch (error) {
        console.error("❌ Error cancelling appointment:", error);
        message.error("Có lỗi xảy ra khi hủy lịch hẹn. Vui lòng thử lại.");
      } finally {
        setLoading(false);
      }
    },
    [loadActivityHistory]
  );

  // Filter activities - giữ nguyên logic gốc
  const getFilteredActivities = useCallback(() => {
    switch (filter) {
      case "donations":
        return activities.filter((a) => a.type === "donation");
      case "requests":
        return activities.filter((a) => a.type === "request");
      default:
        return activities;
    }
  }, [activities, filter]);

  // Statistics - giữ nguyên logic gốc
  const donationCount = activities.filter((a) => a.type === "donation").length;
  const requestCount = activities.filter((a) => a.type === "request").length;
  const completedCount = activities.filter((a) =>
    [
      DONATION_STATUS.COMPLETED,
      REQUEST_STATUS.COMPLETED,
      REQUEST_STATUS.FULFILLED,
    ].includes(a.status)
  ).length;

  useEffect(() => {
    loadActivityHistory();
  }, [loadActivityHistory]);

  // Add focus event listener to refresh data when user returns to the page
  useEffect(() => {
    const handleFocus = () => {
      console.log("🔄 Page focused - refreshing activity history");
      loadActivityHistory();
    };

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log("🔄 Page became visible - refreshing activity history");
        loadActivityHistory();
      }
    };

    window.addEventListener("focus", handleFocus);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      window.removeEventListener("focus", handleFocus);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [loadActivityHistory]);

  return {
    // Data
    activities,
    filteredActivities: getFilteredActivities(),
    loading,
    filter,

    // Statistics
    donationCount,
    requestCount,
    completedCount,

    // Actions
    setFilter,
    loadActivityHistory,
    handleCancelAppointment,

    // Helpers
    getUserBloodType,
  };
};

export default useActivityData;
