import React, { useEffect } from "react";
import { <PERSON>, Tag, Modal, <PERSON>, Typography, Button } from "antd";
import {
  HeartFilled,
  EyeOutlined,
  DeleteOutlined,
  FileTextOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  UserOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import MemberNavbar from "../../components/member/MemberNavbar";
import DetailedStatusTimeline from "../../components/member/DetailedStatusTimeline";
import ActivityDetailModal from "../../components/member/ActivityDetailModal";
import ModernPageHeader from "../../components/member/ModernPageHeader";
import ActivityStatisticsOverview from "../../components/member/ActivityStatisticsOverview";
import ActivityFilterSection from "../../components/member/ActivityFilterSection";
import ActivityLoadingState from "../../components/member/ActivityLoadingState";
import ActivityEmptyState from "../../components/member/ActivityEmptyState";
import useActivityData from "../../hooks/useActivityData";
import useActivityModals from "../../hooks/useActivityModals";
import { formatDate, getStatusInfo } from "../../utils/activityHelpers";
import { DONATION_STATUS } from "../../constants/systemConstants";
import "../../styles/pages/ActivityHistoryPage.scss";

const { Text } = Typography;

/**
 * ActivityHistoryPage - Refactored version
 * Giữ nguyên giao diện và logic từ file gốc nhưng tối ưu hóa cấu trúc code
 */
const ActivityHistoryPage = () => {
  // Sử dụng custom hooks để tách logic
  const {
    activities,
    filteredActivities,
    loading,
    filter,
    donationCount,
    requestCount,
    completedCount,
    setFilter,
    loadActivityHistory,
    handleCancelAppointment,
  } = useActivityData();

  const {
    selectedActivity,
    showWorkflowModal,
    showDetailModal,
    handleViewDetails,
    handleViewWorkflow,
    closeDetailModal,
    closeWorkflowModal,
  } = useActivityModals();

  // Force refresh when component mounts to ensure latest data
  useEffect(() => {
    console.log("🔄 ActivityHistoryPage mounted - refreshing data");
    loadActivityHistory();
  }, [loadActivityHistory]);

  return (
    <div className="activity-history-page">
      <MemberNavbar />

      <div className="activity-content">
        {/* Header Section - Sử dụng ModernPageHeader mới với variant gradient */}
        <ModernPageHeader
          loading={loading}
          onReload={loadActivityHistory}
          variant="gradient"
          theme="light"
        />

        {/* Statistics Overview - Sử dụng component riêng */}
        <ActivityStatisticsOverview
          activities={activities}
          donationCount={donationCount}
          requestCount={requestCount}
          completedCount={completedCount}
        />

        {/* Filter Section - Sử dụng component riêng */}
        <ActivityFilterSection
          filter={filter}
          onFilterChange={setFilter}
          activities={activities}
          donationCount={donationCount}
          requestCount={requestCount}
        />

        {/* Activities List - Giữ nguyên logic render từ file gốc */}
        {loading ? (
          <ActivityLoadingState />
        ) : filteredActivities.length === 0 ? (
          <ActivityEmptyState filter={filter} />
        ) : (
          <div className="activities-list">
            {filteredActivities.map((activity) => {
              const isCancelled = activity.isCancelled;
              const statusInfo = getStatusInfo(activity.status, activity.type);

              return (
                <Card
                  key={activity.id}
                  className={`activity-card ${isCancelled ? "cancelled" : ""} ${
                    activity.type
                  }`}
                  hoverable={!isCancelled}
                >
                  {/* Activity Header - Giữ nguyên từ file gốc */}
                  <div className="activity-header">
                    <div className="activity-info">
                      <div className="activity-title-wrapper">
                        <div className="activity-icon">
                          {activity.type === "donation" ? (
                            <HeartFilled />
                          ) : (
                            <FileTextOutlined />
                          )}
                        </div>
                        <div className="activity-title-content">
                          <h4 className="activity-title">{activity.title}</h4>
                          <Text className="activity-date">
                            <CalendarOutlined className="date-icon" />
                            Tạo lúc: {formatDate(activity.createdAt)}
                          </Text>
                        </div>
                      </div>
                    </div>
                    <div className="activity-status">
                      <Tag
                        className="status-badge"
                        color={isCancelled ? "error" : statusInfo.color}
                      >
                        {isCancelled ? (
                          <Space>
                            <CloseCircleOutlined />
                            <span>ĐÃ HỦY</span>
                          </Space>
                        ) : (
                          statusInfo.text.toUpperCase()
                        )}
                      </Tag>
                    </div>
                  </div>

                  {/* Activity Details - Giữ nguyên từ file gốc */}
                  <div className="activity-details">
                    {/* Appointment Info for Donations */}
                    {activity.type === "donation" && (
                      <div className="detail-section">
                        <h4>
                          <Space>
                            <CalendarOutlined style={{ color: "#722ed1" }} />
                            <span>Thông tin lịch hẹn</span>
                          </Space>
                        </h4>
                        <div className="appointment-info">
                          <div className="info-item">
                            <CalendarOutlined className="info-icon" />
                            <span className="info-label">Ngày hẹn:</span>
                            <span className="info-value">
                              {formatDate(activity.appointmentDate)}
                            </span>
                          </div>
                          <div className="info-item">
                            <ClockCircleOutlined className="info-icon" />
                            <span className="info-label">Khung giờ:</span>
                            <span className="info-value">
                              {activity.timeSlot || "Chưa xác định"}
                            </span>
                          </div>
                          <div className="info-item">
                            <EnvironmentOutlined className="info-icon" />
                            <span className="info-label">Địa điểm:</span>
                            <span className="info-value">
                              {activity.location}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Notes Section */}
                    {activity.notes && (
                      <div className="detail-section">
                        <h4>📝 Ghi chú</h4>
                        <div className="notes">
                          <Text>{activity.notes}</Text>
                        </div>
                      </div>
                    )}

                    {/* Patient Info for Requests */}
                    {activity.type === "request" && activity.patientName && (
                      <div className="detail-section">
                        <h4>
                          <Space>
                            <UserOutlined style={{ color: "#fa8c16" }} />
                            <span>Thông tin bệnh nhân</span>
                          </Space>
                        </h4>
                        <div className="patient-info">
                          <div className="info-item">
                            <UserOutlined className="info-icon" />
                            <span className="info-label">Bệnh nhân:</span>
                            <span className="info-value">
                              {activity.patientName}
                            </span>
                          </div>
                          {activity.hospitalName && (
                            <div className="info-item">
                              <EnvironmentOutlined className="info-icon" />
                              <span className="info-label">Bệnh viện:</span>
                              <span className="info-value">
                                {activity.hospitalName}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Activity Actions - Giữ nguyên từ file gốc */}
                  <div className="activity-actions">
                    <Space size={12} wrap>
                      <Button
                        type="default"
                        icon={<EyeOutlined />}
                        onClick={() => handleViewWorkflow(activity)}
                        className="btn btn-workflow"
                        size="middle"
                      >
                        Xem tiến trình
                      </Button>

                      <Button
                        type="primary"
                        icon={<FileTextOutlined />}
                        onClick={() => handleViewDetails(activity)}
                        className="btn btn-detail"
                        size="middle"
                      >
                        Chi tiết
                      </Button>

                      {activity.type === "donation" &&
                        !isCancelled &&
                        activity.status !== DONATION_STATUS.COMPLETED && (
                          <Button
                            type="default"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => handleCancelAppointment(activity.id)}
                            className="btn btn-danger"
                            size="middle"
                          >
                            Hủy lịch
                          </Button>
                        )}

                      {activity.type === "donation" &&
                        activity.status === DONATION_STATUS.COMPLETED && (
                          <Button
                            type="default"
                            className="btn btn-success"
                            size="middle"
                          >
                            📜 Chứng nhận
                          </Button>
                        )}

                      {isCancelled && (
                        <Text
                          type="secondary"
                          disabled
                          className="cancelled-note"
                        >
                          <Space>
                            <ExclamationCircleOutlined />
                            <span>Không thể thao tác với lịch hẹn đã hủy</span>
                          </Space>
                        </Text>
                      )}
                    </Space>
                  </div>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Detail Modal - Giữ nguyên từ file gốc */}
      <ActivityDetailModal
        visible={showDetailModal}
        onCancel={closeDetailModal}
        activity={selectedActivity}
        getStatusInfo={getStatusInfo}
        formatDate={formatDate}
      />

      {/* Workflow Modal - Giữ nguyên từ file gốc */}
      <Modal
        title={
          <Space>
            <span className={`modal-title-icon ${selectedActivity?.type}`}>
              {selectedActivity?.type === "donation" ? (
                <HeartFilled />
              ) : (
                <FileTextOutlined />
              )}
            </span>
            <Text strong className="modal-title-text">
              Tiến trình{" "}
              {selectedActivity?.type === "donation"
                ? "hiến máu"
                : "yêu cầu máu"}
            </Text>
          </Space>
        }
        open={showWorkflowModal}
        onCancel={closeWorkflowModal}
        footer={[
          <Button
            key="close"
            onClick={closeWorkflowModal}
            className="workflow-close-button"
          >
            Đóng
          </Button>,
        ]}
        width={800}
        className="workflow-modal"
      >
        {selectedActivity && (
          <div>
            <Card size="small" className="workflow-activity-card">
              <Space size={12}>
                <div
                  className={`workflow-activity-icon ${selectedActivity.type}`}
                >
                  {selectedActivity.type === "donation" ? (
                    <HeartFilled />
                  ) : (
                    <FileTextOutlined />
                  )}
                </div>
                <div>
                  <Text strong className="workflow-activity-title">
                    {selectedActivity.title}
                  </Text>
                  <br />
                  <Text className="workflow-activity-date">
                    Tạo lúc: {formatDate(selectedActivity.createdAt)}
                  </Text>
                </div>
              </Space>
            </Card>

            <DetailedStatusTimeline
              activity={selectedActivity}
              workflowType={selectedActivity.type}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ActivityHistoryPage;
