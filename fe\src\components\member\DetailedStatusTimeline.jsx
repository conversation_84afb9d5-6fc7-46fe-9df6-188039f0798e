import React from "react";
import { Timeline, Card, Typography, Space, Tag } from "antd";
import { 
  ClockCircleOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";
import "../../styles/components/DetailedStatusTimeline.scss";

const { Text, Title } = Typography;

/**
 * Component hiển thị timeline chi tiết cho lịch sử hoạt động của Member
 * Hiển thị đầy đủ tiến trình xử lý với thời gian cụ thể
 */
const DetailedStatusTimeline = ({ activity, workflowType = "request" }) => {
  if (!activity) return null;

  // Mapping status names theo yêu cầu UI
  const getStatusDisplayName = (status, type) => {
    if (type === "request") {
      switch (status) {
        case 0: // PENDING
          return "Đang chờ xử lý";
        case 1: // ACCEPTED/APPROVED
          return "Đã chấp nhận";
        case 2: // COMPLETED
          return "Hoàn thành";
        case 3: // REJECTED
          return "Từ chối";
        default:
          return "Không xác định";
      }
    } else {
      // Donation workflow
      switch (status) {
        case 0:
          return "Đăng ký thành công";
        case 1:
          return "Đã khám sàng lọc";
        case 2:
          return "Đã hiến máu";
        case 3:
          return "Đã xét nghiệm";
        case 4:
          return "Hoàn thành";
        case 5:
          return "Đã nhập kho";
        case -1:
          return "Không đủ điều kiện";
        default:
          return "Không xác định";
      }
    }
  };

  // Get icon cho từng status
  const getStatusIcon = (status, type) => {
    if (type === "request") {
      switch (status) {
        case 0:
          return <ClockCircleOutlined style={{ color: "#faad14" }} />;
        case 1:
          return <CheckCircleOutlined style={{ color: "#52c41a" }} />;
        case 2:
          return <CheckCircleOutlined style={{ color: "#1890ff" }} />;
        case 3:
          return <CloseCircleOutlined style={{ color: "#ff4d4f" }} />;
        default:
          return <ExclamationCircleOutlined style={{ color: "#d9d9d9" }} />;
      }
    } else {
      switch (status) {
        case 0:
        case 1:
        case 2:
        case 3:
          return <CheckCircleOutlined style={{ color: "#52c41a" }} />;
        case 4:
        case 5:
          return <CheckCircleOutlined style={{ color: "#1890ff" }} />;
        case -1:
          return <CloseCircleOutlined style={{ color: "#ff4d4f" }} />;
        default:
          return <ExclamationCircleOutlined style={{ color: "#d9d9d9" }} />;
      }
    }
  };

  // Tạo timeline items từ activity history
  const createTimelineItems = () => {
    const items = [];
    
    // Thêm thời điểm tạo yêu cầu
    items.push({
      dot: <FileTextOutlined style={{ color: "#1890ff" }} />,
      color: "#1890ff",
      children: (
        <div className="timeline-item">
          <div className="timeline-content">
            <Text strong>Tạo yêu cầu</Text>
            <br />
            <Text type="secondary">
              {dayjs(activity.createdAt).format("HH:mm [ngày] DD/MM/YYYY")}
            </Text>
          </div>
        </div>
      ),
    });

    // Thêm các status updates từ history
    if (activity.statusHistory && activity.statusHistory.length > 0) {
      activity.statusHistory.forEach((historyItem, index) => {
        const statusName = getStatusDisplayName(historyItem.status, workflowType);
        const icon = getStatusIcon(historyItem.status, workflowType);
        
        items.push({
          dot: icon,
          children: (
            <div className="timeline-item">
              <div className="timeline-content">
                <Text strong>{statusName}</Text>
                <br />
                <Text type="secondary">
                  {dayjs(historyItem.updatedAt).format("HH:mm [ngày] DD/MM/YYYY")}
                </Text>
                {historyItem.notes && (
                  <>
                    <br />
                    <Text type="secondary" italic>
                      {historyItem.notes}
                    </Text>
                  </>
                )}
              </div>
            </div>
          ),
        });
      });
    } else {
      // Fallback: nếu không có statusHistory, tạo từ current status
      const currentStatusName = getStatusDisplayName(activity.status, workflowType);
      const currentIcon = getStatusIcon(activity.status, workflowType);
      
      items.push({
        dot: currentIcon,
        children: (
          <div className="timeline-item">
            <div className="timeline-content">
              <Text strong>{currentStatusName}</Text>
              <br />
              <Text type="secondary">
                {dayjs(activity.updatedAt || activity.createdAt).format("HH:mm [ngày] DD/MM/YYYY")}
              </Text>
            </div>
          </div>
        ),
      });
    }

    return items;
  };

  const timelineItems = createTimelineItems();

  return (
    <Card 
      title={
        <Space>
          <ClockCircleOutlined />
          <span>Lịch sử xử lý</span>
        </Space>
      }
      className="detailed-status-timeline"
      size="small"
    >
      <Timeline
        mode="left"
        items={timelineItems}
        className="status-timeline"
      />
      
      {/* Hiển thị trạng thái hiện tại */}
      <div className="current-status-summary">
        <Text type="secondary">Trạng thái hiện tại: </Text>
        <Tag 
          color={
            activity.status === 2 ? "success" : 
            activity.status === 3 ? "error" : 
            activity.status === 1 ? "processing" : "warning"
          }
        >
          {getStatusDisplayName(activity.status, workflowType)}
        </Tag>
      </div>
    </Card>
  );
};

export default DetailedStatusTimeline;
